# Tên của workflow
name: Build and Deploy Angular App to AWS S3

# Quy định khi nào workflow này sẽ được kích hoạt
on:
  push:
    branches:
      - main  # Chạy khi có push lên nhánh main

# Các công việc (jobs) sẽ được thực thi
jobs:
  # --- JOB 1: BUILD ---
  # Công việc này chịu trách nhiệm build ứng dụng và tạo ra artifact
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        # Action để tải code từ repository về
        uses: actions/checkout@v4

      - name: Setup Node.js
        # Action để cài đặt Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20' # Chỉ định phiên bản Node.js bạn dùng

      - name: Install Angular CLI
        # Cài đặt Angular CLI
        run: npm install -g @angular/cli

      - name: Install dependencies
        # Cài đặt các gói phụ thuộc của dự án
        run: npm install

      - name: Build Angular App
        # Build ứng dụng với cấu hình production
        run: ng build --configuration=production

      - name: Upload build artifact
        # Lưu trữ thư mục build (dist) để job deploy có thể sử dụng
        uses: actions/upload-artifact@v4
        with:
          name: angular-build-artifact # Tên của artifact
          path: ./dist # Đường dẫn đến thư mục cần lưu trữ

  # --- JOB 2: DEPLOY ---
  # Công việc này sẽ chạy sau khi job 'build' thành công
  deploy:
    # 'needs: build' đảm bảo job này chỉ chạy khi job 'build' hoàn tất
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download build artifact
        # Tải xuống artifact đã được tạo từ job 'build'
        uses: actions/download-artifact@v4
        with:
          name: angular-build-artifact # Tên của artifact phải khớp với tên đã upload

      - name: Configure AWS Credentials
        # Action để cấu hình thông tin xác thực AWS
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy to S3
        # Sử dụng AWS CLI để đồng bộ hóa thư mục build với S3 bucket
        # --delete sẽ xóa các file không còn tồn tại trong thư mục build khỏi bucket
        # Lưu ý: Thay thế <tên-dự-án-angular-của-bạn> bằng tên dự án của bạn
        run: |
          aws s3 sync ./springboot-chat-123/browser/ s3://${{ secrets.AWS_S3_BUCKET }} --delete
