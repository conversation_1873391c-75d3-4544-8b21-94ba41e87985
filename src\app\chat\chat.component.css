.chat-container {
  display: flex;
  flex-direction: column;
  height: 90vh; /* Adjust as needed */
  max-width: 800px;
  margin: 20px auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;
  background-color: #f7f7f7;
}

.chat-header {
  background-color: #343541;
  color: white;
  padding: 15px 20px;
  text-align: center;
  border-bottom: 1px solid #202123;
}

.chat-header h2 {
  margin: 0;
  font-size: 1.2em;
}

.chat-messages {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #fff;
}

.message-wrapper {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start; /* Ensure messages align to top */
}

.message {
  max-width: 85%;
  padding: 10px 15px;
  border-radius: 18px;
  line-height: 1.5;
  word-wrap: break-word; /* Ensure long words break */
}

.sender-label {
  font-weight: bold;
  margin-right: 8px;
  min-width: 40px; /* Give a fixed width for labels */
  text-align: right; /* Align label to the right of its space */
  display: inline-block; /* Allow min-width to work */
}

.user-message {
  background-color: #dcf8c6; /* Light green */
  align-self: flex-end; /* Align to right */
  margin-left: auto; /* Push to right */
  border-bottom-right-radius: 2px; /* Small corner adjustment */
}

.user-message .sender-label {
  color: #2196f3; /* Blue for user */
}

.bot-message {
  background-color: #e0e0e0; /* Light gray */
  align-self: flex-start; /* Align to left */
  margin-right: auto; /* Push to left */
  border-bottom-left-radius: 2px; /* Small corner adjustment */
}

.bot-message .sender-label {
  color: #4caf50; /* Green for bot */
}

.chat-input-area {
  display: flex;
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f0f0f0;
}

.chat-input {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 1em;
  resize: none; /* Disable vertical resize */
  overflow: hidden; /* Hide scrollbar */
  min-height: 40px; /* Initial height */
  line-height: 20px;
}

.chat-input:focus {
  outline: none;
  border-color: #66afe9;
  box-shadow: 0 0 8px rgba(102, 175, 233, 0.6);
}

.send-button {
  background-color: #4CAF50; /* Green */
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  margin-left: 10px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.3s ease;
}

.send-button:hover:not(:disabled) {
  background-color: #45a049;
}

.send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Loading dots animation */
.loading-dots {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 1em; /* Adjust height to match text line */
}

.loading-dots span {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #888;
  border-radius: 50%;
  margin: 0 2px;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}