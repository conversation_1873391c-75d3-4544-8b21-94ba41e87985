import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

interface ChatResponse {
  answer: string;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private apiUrl = `${environment.apiUrl}/chat`;
  private userId: string;

  constructor(private http: HttpClient) {
    // Khởi tạo hoặc lấy userId từ localStorage
    const storedUserId = localStorage.getItem('chatbotUserId');
    if (storedUserId) {
      this.userId = storedUserId;
    } else {
      this.userId = crypto.randomUUID(); // Tạo UUID ngẫu nhiên
      localStorage.setItem('chatbotUserId', this.userId);
    }
  }

  sendMessage(query: string): Observable<string> {
    const body = { query: query };
    const headers = new HttpHeaders().set('X-User-ID', this.userId);

    return this.http.post<ChatResponse>(this.apiUrl, body, { headers: headers }).pipe(
      map(response => response.answer)
    );
  }

  // Nếu có phương thức stream, cũng cần cập nhật tương tự
  // Ví dụ:
  // sendStreamMessage(query: string): Observable<string> {
  //   const body = { query: query };
  //   const headers = new HttpHeaders().set('X-User-ID', this.userId);
  //   return this.http.post(this.apiUrl + '/stream', body, { headers: headers, responseType: 'text' });
  // }
}
